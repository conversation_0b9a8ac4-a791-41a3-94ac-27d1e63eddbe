# -*- coding: utf-8 -*-
"""
نظام معالجة الأخطاء المحسن
Enhanced Error Handling System
"""

import sys
import traceback
import logging
import os
from datetime import datetime
from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal

class ErrorHandler(QObject):
    """معالج الأخطاء المحسن"""
    
    error_occurred = pyqtSignal(str, str)  # نوع الخطأ، الرسالة
    
    def __init__(self):
        super().__init__()
        self.setup_logging()
        self.install_exception_handler()
    
    def setup_logging(self):
        """إعداد نظام السجلات"""
        try:
            # إنشاء مجلد السجلات
            logs_dir = "logs"
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)
            
            # إعداد ملف السجل
            log_filename = os.path.join(logs_dir, f"app_{datetime.now().strftime('%Y%m%d')}.log")
            
            # إعداد التنسيق
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            # إعداد معالج الملف
            file_handler = logging.FileHandler(log_filename, encoding='utf-8')
            file_handler.setFormatter(formatter)
            file_handler.setLevel(logging.DEBUG)
            
            # إعداد معالج وحدة التحكم
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            console_handler.setLevel(logging.INFO)
            
            # إعداد السجل الرئيسي
            self.logger = logging.getLogger('SmartFinish')
            self.logger.setLevel(logging.DEBUG)
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
            
            self.logger.info("تم تهيئة نظام السجلات بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في إعداد نظام السجلات: {str(e)}")
    
    def install_exception_handler(self):
        """تثبيت معالج الاستثناءات العام"""
        sys.excepthook = self.handle_exception
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """معالجة الاستثناءات غير المتوقعة"""
        if issubclass(exc_type, KeyboardInterrupt):
            # السماح بـ Ctrl+C
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # تسجيل الخطأ
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        self.logger.critical(f"خطأ غير متوقع: {error_msg}")
        
        # إرسال إشارة الخطأ
        self.error_occurred.emit("خطأ نظام", str(exc_value))
        
        # عرض رسالة للمستخدم
        self.show_error_dialog("خطأ غير متوقع", str(exc_value))
    
    def log_error(self, error_type, message, details=None):
        """تسجيل خطأ مخصص"""
        try:
            full_message = f"{error_type}: {message}"
            if details:
                full_message += f" - التفاصيل: {details}"
            
            self.logger.error(full_message)
            
            # إرسال إشارة الخطأ
            self.error_occurred.emit(error_type, message)
            
        except Exception as e:
            print(f"❌ خطأ في تسجيل الخطأ: {str(e)}")
    
    def log_warning(self, message, details=None):
        """تسجيل تحذير"""
        try:
            full_message = message
            if details:
                full_message += f" - التفاصيل: {details}"
            
            self.logger.warning(full_message)
            
        except Exception as e:
            print(f"❌ خطأ في تسجيل التحذير: {str(e)}")
    
    def log_info(self, message):
        """تسجيل معلومة"""
        try:
            self.logger.info(message)
        except Exception as e:
            print(f"❌ خطأ في تسجيل المعلومة: {str(e)}")
    
    def show_error_dialog(self, title, message):
        """عرض نافذة خطأ للمستخدم"""
        try:
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setDetailedText("يرجى مراجعة ملف السجل للحصول على تفاصيل أكثر")
            msg_box.exec_()
        except Exception as e:
            print(f"❌ خطأ في عرض نافذة الخطأ: {str(e)}")

class DatabaseErrorHandler:
    """معالج أخطاء قاعدة البيانات"""
    
    @staticmethod
    def handle_db_error(error, operation="عملية قاعدة البيانات"):
        """معالجة أخطاء قاعدة البيانات"""
        error_msg = str(error)
        
        # تصنيف الأخطاء
        if "UNIQUE constraint failed" in error_msg:
            return "خطأ: البيانات مكررة. يرجى التحقق من البيانات المدخلة."
        elif "NOT NULL constraint failed" in error_msg:
            return "خطأ: يرجى ملء جميع الحقول المطلوبة."
        elif "no such table" in error_msg:
            return "خطأ: جدول قاعدة البيانات غير موجود. يرجى إعادة تهيئة البرنامج."
        elif "database is locked" in error_msg:
            return "خطأ: قاعدة البيانات مقفلة. يرجى المحاولة مرة أخرى."
        elif "disk I/O error" in error_msg:
            return "خطأ: مشكلة في القرص الصلب. يرجى التحقق من مساحة القرص."
        else:
            return f"خطأ في {operation}: {error_msg}"

class ValidationErrorHandler:
    """معالج أخطاء التحقق من البيانات"""
    
    @staticmethod
    def validate_required_fields(data, required_fields):
        """التحقق من الحقول المطلوبة"""
        missing_fields = []
        for field in required_fields:
            if not data.get(field) or str(data.get(field)).strip() == "":
                missing_fields.append(field)
        
        if missing_fields:
            return f"الحقول التالية مطلوبة: {', '.join(missing_fields)}"
        return None
    
    @staticmethod
    def validate_email(email):
        """التحقق من صحة البريد الإلكتروني"""
        import re
        if not email:
            return None
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, email):
            return "البريد الإلكتروني غير صحيح"
        return None
    
    @staticmethod
    def validate_phone(phone):
        """التحقق من صحة رقم الهاتف"""
        if not phone:
            return None
        
        # إزالة المسافات والرموز
        clean_phone = ''.join(filter(str.isdigit, phone))
        
        if len(clean_phone) < 10:
            return "رقم الهاتف قصير جداً"
        elif len(clean_phone) > 15:
            return "رقم الهاتف طويل جداً"
        
        return None

# إنشاء معالج الأخطاء العام
error_handler = ErrorHandler()

def safe_execute(func, *args, **kwargs):
    """تنفيذ آمن للدوال مع معالجة الأخطاء"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_handler.log_error("خطأ في التنفيذ", str(e), f"الدالة: {func.__name__}")
        return None

if __name__ == "__main__":
    # اختبار نظام معالجة الأخطاء
    print("🧪 اختبار نظام معالجة الأخطاء...")
    
    handler = ErrorHandler()
    handler.log_info("تم بدء اختبار نظام معالجة الأخطاء")
    handler.log_warning("هذا تحذير تجريبي")
    handler.log_error("خطأ تجريبي", "هذا خطأ للاختبار")
    
    print("✅ تم اختبار نظام معالجة الأخطاء بنجاح")
