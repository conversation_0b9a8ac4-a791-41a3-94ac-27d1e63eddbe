"""
واجهة العملاء المتطورة والمتقدمة جداً مع استغلال كامل للمساحة
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFrame, QHeaderView, QDialog, QSplitter)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor, QFont

from ui.unified_styles import StyledTable, UnifiedStyles
from database import Client
from utils import show_error_message, show_info_message, show_confirmation_message

class AdvancedClientsWidget(QWidget):
    """واجهة العملاء المتطورة والمتقدمة جداً مع استغلال كامل للمساحة"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_maximized_ui()
        self.load_clients()
        self.update_stats()

    def init_maximized_ui(self):
        """إنشاء الواجهة المتطورة مع استغلال كامل للمساحة"""
        layout = QVBoxLayout()
        layout.setContentsMargins(2, 2, 2, 2)  # هوامش صغيرة جداً
        layout.setSpacing(5)  # مسافات صغيرة

        # إنشاء رأس مضغوط مع إحصائيات
        self.create_compact_header(layout)

        # إنشاء شريط البحث والفلترة المضغوط
        self.create_compact_search_bar(layout)

        # إنشاء الجدول المتطور الذي يستغل كامل المساحة
        self.create_maximized_table(layout)

        # إنشاء شريط الأدوات المضغوط
        self.create_compact_toolbar(layout)

        self.setLayout(layout)

    def create_compact_header(self, layout):
        """إنشاء رأس مضغوط مع إحصائيات"""
        header_frame = QFrame()
        header_frame.setMaximumHeight(60)  # ارتفاع محدود
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.3 #764ba2, stop:0.7 #f093fb, stop:1 #4facfe);
                border-radius: 12px;
                padding: 8px;
                margin: 2px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)

        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(15, 8, 15, 8)

        # العنوان الرئيسي مضغوط
        title_label = QLabel("🤝 إدارة العملاء المتطورة")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)

        # إحصائيات سريعة مضغوطة
        self.stats_label = QLabel("📊 جاري التحميل...")
        self.stats_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 16px;
                font-weight: 600;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                padding: 8px 15px;
                border: 2px solid rgba(255, 255, 255, 0.2);
            }
        """)

        header_layout.addWidget(title_label, 2)
        header_layout.addWidget(self.stats_label, 1)
        header_frame.setLayout(header_layout)
        layout.addWidget(header_frame)

    def create_compact_search_bar(self, layout):
        """إنشاء شريط بحث مضغوط"""
        search_frame = QFrame()
        search_frame.setMaximumHeight(50)  # ارتفاع محدود
        search_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 12px;
                padding: 8px;
                border: 2px solid rgba(102, 126, 234, 0.3);
            }
        """)

        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(10, 5, 10, 5)
        search_layout.setSpacing(10)

        # حقل البحث المضغوط
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔍 البحث في العملاء...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid rgba(102, 126, 234, 0.3);
                border-radius: 10px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 500;
                color: #2d3748;
                min-height: 20px;
            }
            QLineEdit:focus {
                border: 2px solid #667eea;
                background: rgba(102, 126, 234, 0.05);
            }
        """)
        self.search_edit.textChanged.connect(self.filter_clients)

        # أزرار الفلترة المضغوطة
        filter_buttons = [
            ("🌟 الكل", "all", "#667eea"),
            ("💰 موجب", "positive", "#10b981"),
            ("⚠️ سالب", "negative", "#ef4444"),
            ("⚪ صفر", "zero", "#f59e0b")
        ]

        self.filter_buttons = {}
        for text, filter_type, color in filter_buttons:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 6px 12px;
                    font-size: 14px;
                    font-weight: 600;
                    min-width: 80px;
                    max-height: 30px;
                }}
                QPushButton:hover {{
                    background: {self.lighten_color(color)};
                }}
                QPushButton:pressed {{
                    background: {self.darken_color(color)};
                }}
            """)
            btn.clicked.connect(lambda checked, ft=filter_type: self.filter_clients_by_balance(ft))
            self.filter_buttons[filter_type] = btn
            search_layout.addWidget(btn)

        search_layout.insertWidget(0, self.search_edit, 3)
        search_frame.setLayout(search_layout)
        layout.addWidget(search_frame)

    def create_maximized_table(self, layout):
        """إنشاء الجدول المتطور الذي يستغل كامل المساحة"""
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.98);
                border-radius: 15px;
                padding: 8px;
                border: 2px solid rgba(102, 126, 234, 0.2);
            }
        """)

        table_layout = QVBoxLayout()
        table_layout.setContentsMargins(5, 5, 5, 5)

        # إنشاء الجدول المتطور مع المزيد من الأعمدة
        styled_table = StyledTable()
        self.clients_table = styled_table.table
        self.clients_table.setColumnCount(10)  # المزيد من الأعمدة
        self.clients_table.setHorizontalHeaderLabels([
            "🆔 ID", "👤 الاسم الكامل", "📱 الهاتف الأساسي", "📧 البريد الإلكتروني",
            "📍 العنوان التفصيلي", "💰 الرصيد الحالي", "📝 الملاحظات", "🔄 الحالة",
            "📅 تاريخ الإنشاء", "⭐ التقييم"
        ])

        # تخصيص عرض الأعمدة لاستغلال كامل المساحة
        header = self.clients_table.horizontalHeader()
        header.setSectionResizeMode(0, header.Fixed)      # الرقم
        header.setSectionResizeMode(1, header.Stretch)    # الاسم
        header.setSectionResizeMode(2, header.Fixed)      # الهاتف
        header.setSectionResizeMode(3, header.Stretch)    # البريد
        header.setSectionResizeMode(4, header.Stretch)    # العنوان
        header.setSectionResizeMode(5, header.Fixed)      # الرصيد
        header.setSectionResizeMode(6, header.Stretch)    # ملاحظات
        header.setSectionResizeMode(7, header.Fixed)      # الحالة
        header.setSectionResizeMode(8, header.Fixed)      # التاريخ
        header.setSectionResizeMode(9, header.Fixed)      # التقييم

        # تحديد عرض الأعمدة الثابتة
        self.clients_table.setColumnWidth(0, 60)    # الرقم
        self.clients_table.setColumnWidth(2, 130)   # الهاتف
        self.clients_table.setColumnWidth(5, 110)   # الرصيد
        self.clients_table.setColumnWidth(7, 80)    # الحالة
        self.clients_table.setColumnWidth(8, 100)   # التاريخ
        self.clients_table.setColumnWidth(9, 80)    # التقييم

        # تمكين التلميحات والتفاعل المتطور
        self.clients_table.setMouseTracking(True)
        self.clients_table.cellEntered.connect(self.show_advanced_tooltip)
        self.clients_table.doubleClicked.connect(self.edit_client)
        self.clients_table.setAlternatingRowColors(True)

        # تحسين ارتفاع الصفوف
        self.clients_table.verticalHeader().setDefaultSectionSize(45)

        # إضافة المزيد من المساحة للجدول
        table_layout.addWidget(self.clients_table, 1)  # stretch factor = 1
        table_frame.setLayout(table_layout)
        layout.addWidget(table_frame, 1)  # الجدول يأخذ معظم المساحة

    def create_compact_toolbar(self, layout):
        """إنشاء شريط الأدوات المضغوط"""
        toolbar_frame = QFrame()
        toolbar_frame.setMaximumHeight(50)  # ارتفاع محدود
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.9);
                border-radius: 12px;
                padding: 5px;
                border: 2px solid rgba(102, 126, 234, 0.3);
            }
        """)

        toolbar_layout = QHBoxLayout()
        toolbar_layout.setContentsMargins(10, 5, 10, 5)
        toolbar_layout.setSpacing(8)

        # أزرار الإجراءات المضغوطة
        actions = [
            ("➕ إضافة", self.add_client, "#10b981"),
            ("✏️ تعديل", self.edit_client, "#3b82f6"),
            ("🗑️ حذف", self.delete_client, "#ef4444"),
            ("👁️ عرض", self.view_client, "#8b5cf6"),
            ("📊 تقرير", self.generate_client_report, "#f59e0b"),
            ("📤 تصدير", self.export_clients, "#06b6d4"),
            ("🔄 تحديث", self.load_clients, "#6b7280")
        ]

        for text, callback, color in actions:
            btn = self.create_compact_button(text, callback, color)
            toolbar_layout.addWidget(btn)

        # إضافة مساحة مرنة
        toolbar_layout.addStretch()

        # عداد العملاء المحددين مضغوط
        self.selection_label = QLabel("📋 لم يتم تحديد أي عميل")
        self.selection_label.setStyleSheet("""
            QLabel {
                color: #6b7280;
                font-size: 14px;
                font-weight: 500;
                padding: 5px 10px;
                background: rgba(107, 114, 128, 0.1);
                border-radius: 8px;
            }
        """)
        toolbar_layout.addWidget(self.selection_label)

        toolbar_frame.setLayout(toolbar_layout)
        layout.addWidget(toolbar_frame)

    def create_compact_button(self, text, callback, color):
        """إنشاء زر مضغوط"""
        btn = QPushButton(text)
        btn.setStyleSheet(f"""
            QPushButton {{
                background: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 6px 12px;
                font-size: 14px;
                font-weight: 600;
                min-width: 80px;
                max-height: 32px;
            }}
            QPushButton:hover {{
                background: {self.lighten_color(color)};
            }}
            QPushButton:pressed {{
                background: {self.darken_color(color)};
            }}
            QPushButton:disabled {{
                background: #9ca3af;
                color: #d1d5db;
            }}
        """)
        btn.clicked.connect(callback)
        return btn

    def lighten_color(self, color):
        """تفتيح اللون"""
        color_map = {
            "#10b981": "#34d399", "#3b82f6": "#60a5fa", "#ef4444": "#f87171",
            "#8b5cf6": "#a78bfa", "#f59e0b": "#fbbf24", "#06b6d4": "#22d3ee",
            "#6b7280": "#9ca3af", "#667eea": "#818cf8"
        }
        return color_map.get(color, color)

    def darken_color(self, color):
        """تغميق اللون"""
        color_map = {
            "#10b981": "#059669", "#3b82f6": "#2563eb", "#ef4444": "#dc2626",
            "#8b5cf6": "#7c3aed", "#f59e0b": "#d97706", "#06b6d4": "#0891b2",
            "#6b7280": "#4b5563", "#667eea": "#5b21b6"
        }
        return color_map.get(color, color)

    # دوال أساسية (سيتم إضافة المزيد لاحقاً)
    def load_clients(self):
        """تحميل العملاء"""
        try:
            clients = self.session.query(Client).all()
            self.populate_advanced_table(clients)
            self.update_stats()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحميل العملاء: {str(e)}")

    def update_stats(self):
        """تحديث الإحصائيات"""
        try:
            total_clients = self.session.query(Client).count()
            positive_clients = self.session.query(Client).filter(Client.balance > 0).count()
            negative_clients = self.session.query(Client).filter(Client.balance < 0).count()

            self.stats_label.setText(f"📊 {total_clients} عميل | 💰 {positive_clients} موجب | ⚠️ {negative_clients} سالب")
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def filter_clients(self):
        """فلترة العملاء بالبحث المتطور"""
        search_text = self.search_edit.text().strip().lower()

        if not search_text:
            self.load_clients()
            return

        try:
            # بحث متطور في جميع الحقول
            clients = self.session.query(Client).filter(
                Client.name.like(f"%{search_text}%") |
                Client.phone.like(f"%{search_text}%") |
                Client.email.like(f"%{search_text}%") |
                Client.address.like(f"%{search_text}%") |
                Client.notes.like(f"%{search_text}%")
            ).all()

            self.populate_advanced_table(clients)

            # تحديث عداد النتائج
            self.selection_label.setText(f"🔍 تم العثور على {len(clients)} عميل")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def filter_clients_by_balance(self, balance_type):
        """فلترة العملاء حسب الرصيد مع تحديث الواجهة"""
        try:
            # إعادة تعيين ألوان الأزرار
            for btn_type, btn in self.filter_buttons.items():
                if btn_type == balance_type:
                    # تمييز الزر المحدد
                    btn.setStyleSheet(btn.styleSheet().replace("background:", "background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #ffd700, stop:1") + ";")
                else:
                    # إعادة الزر للحالة العادية
                    original_color = {"all": "#667eea", "positive": "#10b981", "negative": "#ef4444", "zero": "#f59e0b"}[btn_type]
                    btn.setStyleSheet(f"""
                        QPushButton {{
                            background: {original_color};
                            color: white;
                            border: none;
                            border-radius: 8px;
                            padding: 6px 12px;
                            font-size: 14px;
                            font-weight: 600;
                            min-width: 80px;
                            max-height: 30px;
                        }}
                        QPushButton:hover {{
                            background: {self.lighten_color(original_color)};
                        }}
                        QPushButton:pressed {{
                            background: {self.darken_color(original_color)};
                        }}
                    """)

            # تطبيق الفلترة
            if balance_type == 'positive':
                clients = self.session.query(Client).filter(Client.balance > 0).all()
                filter_text = "العملاء برصيد موجب"
            elif balance_type == 'negative':
                clients = self.session.query(Client).filter(Client.balance < 0).all()
                filter_text = "العملاء برصيد سالب"
            elif balance_type == 'zero':
                clients = self.session.query(Client).filter(Client.balance == 0).all()
                filter_text = "العملاء برصيد صفر"
            else:
                clients = self.session.query(Client).all()
                filter_text = "جميع العملاء"

            self.populate_advanced_table(clients)

            # تحديث عداد النتائج
            self.selection_label.setText(f"📊 {filter_text}: {len(clients)} عميل")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء الفلترة: {str(e)}")

    def show_advanced_tooltip(self, row, column):
        """عرض تلميح متطور للخلية"""
        try:
            item = self.clients_table.item(row, column)
            if not item or not item.text():
                return

            # تلميحات مخصصة حسب العمود
            column_tooltips = {
                0: f"🆔 رقم العميل: {item.text()}",
                1: f"👤 اسم العميل: {item.text()}",
                2: f"📱 رقم الهاتف: {item.text()}\n💡 انقر نقرتين للاتصال",
                3: f"📧 البريد الإلكتروني: {item.text()}\n💡 انقر نقرتين لإرسال بريد",
                4: f"📍 العنوان: {item.text()}",
                5: f"💰 الرصيد الحالي: {item.text()}\n💡 انقر نقرتين لتعديل الرصيد",
                6: f"📝 الملاحظات: {item.text()}",
                7: f"🔄 حالة العميل: {item.text()}",
                8: f"📅 تاريخ الإنشاء: {item.text()}",
                9: f"⭐ تقييم العميل: {item.text()}\n💡 يعتمد على الرصيد والنشاط"
            }

            tooltip = column_tooltips.get(column, f"القيمة: {item.text()}")
            item.setToolTip(tooltip)

        except Exception as e:
            print(f"خطأ في عرض التلميح: {str(e)}")

    def populate_advanced_table(self, clients):
        """ملء الجدول المتطور بالبيانات"""
        self.clients_table.setRowCount(0)

        for row, client in enumerate(clients):
            self.clients_table.insertRow(row)

            # 1. الرقم
            id_item = QTableWidgetItem(str(client.id))
            id_item.setTextAlignment(Qt.AlignCenter)
            id_item.setFont(QFont("Arial", 10, QFont.Bold))
            self.clients_table.setItem(row, 0, id_item)

            # 2. الاسم الكامل
            name_item = QTableWidgetItem(client.name)
            name_item.setFont(QFont("Arial", 11, QFont.Bold))
            name_item.setForeground(QColor("#1f2937"))
            self.clients_table.setItem(row, 1, name_item)

            # 3. الهاتف الأساسي
            phone_item = QTableWidgetItem(client.phone or "غير محدد")
            phone_item.setTextAlignment(Qt.AlignCenter)
            phone_item.setFont(QFont("Courier", 10))
            if client.phone:
                phone_item.setForeground(QColor("#059669"))
            else:
                phone_item.setForeground(QColor("#9ca3af"))
            self.clients_table.setItem(row, 2, phone_item)

            # 4. البريد الإلكتروني
            email_item = QTableWidgetItem(client.email or "غير محدد")
            email_item.setFont(QFont("Arial", 10))
            if client.email:
                email_item.setForeground(QColor("#3b82f6"))
            else:
                email_item.setForeground(QColor("#9ca3af"))
            self.clients_table.setItem(row, 3, email_item)

            # 5. العنوان التفصيلي
            address_item = QTableWidgetItem(client.address or "غير محدد")
            address_item.setFont(QFont("Arial", 10))
            if client.address:
                address_item.setForeground(QColor("#6b7280"))
            else:
                address_item.setForeground(QColor("#9ca3af"))
            self.clients_table.setItem(row, 4, address_item)

            # 6. الرصيد الحالي مع تلوين متطور
            balance_item = QTableWidgetItem(f"{client.balance:.2f} ر.س")
            balance_item.setTextAlignment(Qt.AlignCenter)
            balance_item.setFont(QFont("Arial", 11, QFont.Bold))

            if client.balance < 0:
                balance_item.setForeground(QColor("#dc2626"))
                balance_item.setBackground(QColor("#fef2f2"))
                balance_item.setToolTip(f"💸 مبلغ مستحق على العميل: {abs(client.balance):.2f} ر.س")
            elif client.balance > 0:
                balance_item.setForeground(QColor("#059669"))
                balance_item.setBackground(QColor("#f0fdf4"))
                balance_item.setToolTip(f"💰 مبلغ مستحق للعميل: {client.balance:.2f} ر.س")
            else:
                balance_item.setForeground(QColor("#6b7280"))
                balance_item.setBackground(QColor("#f9fafb"))
                balance_item.setToolTip("⚪ لا يوجد رصيد مستحق")

            self.clients_table.setItem(row, 5, balance_item)

            # 7. الملاحظات
            notes_item = QTableWidgetItem(client.notes or "لا توجد ملاحظات")
            notes_item.setFont(QFont("Arial", 10))
            if client.notes:
                notes_item.setForeground(QColor("#374151"))
            else:
                notes_item.setForeground(QColor("#9ca3af"))
                notes_item.setFont(QFont("Arial", 10, QFont.StyleItalic))
            self.clients_table.setItem(row, 6, notes_item)

            # 8. الحالة مع إضافة حالة "عادي"
            if client.balance > 0:
                status_text = "🟢 نشط"
                status_color = QColor("#059669")
                status_bg = QColor("#f0fdf4")
            elif client.balance == 0:
                status_text = "🔵 عادي"
                status_color = QColor("#3182ce")
                status_bg = QColor("#bee3f8")
            else:
                status_text = "🔴 مدين"
                status_color = QColor("#dc2626")
                status_bg = QColor("#fef2f2")

            status_item = QTableWidgetItem(status_text)
            status_item.setTextAlignment(Qt.AlignCenter)
            status_item.setFont(QFont("Segoe UI", 12, QFont.Bold))  # خط موحد
            status_item.setForeground(status_color)
            status_item.setBackground(status_bg)

            # إضافة تلميح للحالة
            if client.balance > 0:
                status_item.setToolTip(f"🟢 عميل نشط\n💰 الرصيد: {client.balance:,.2f} ر.س")
            elif client.balance == 0:
                status_item.setToolTip(f"🔵 عميل عادي\n💰 الرصيد: صفر")
            else:
                status_item.setToolTip(f"🔴 عميل مدين\n💰 المبلغ المستحق: {abs(client.balance):,.2f} ر.س")

            self.clients_table.setItem(row, 7, status_item)

            # 9. تاريخ الإنشاء
            if hasattr(client, 'created_at') and client.created_at:
                date_text = client.created_at.strftime("%Y-%m-%d")
            else:
                date_text = "غير محدد"
            date_item = QTableWidgetItem(date_text)
            date_item.setTextAlignment(Qt.AlignCenter)
            date_item.setFont(QFont("Courier", 9))
            date_item.setForeground(QColor("#6b7280"))
            self.clients_table.setItem(row, 8, date_item)

            # 10. التقييم (حسب الرصيد والنشاط)
            if client.balance > 1000:
                rating = "⭐⭐⭐⭐⭐"
                rating_color = QColor("#fbbf24")
            elif client.balance > 500:
                rating = "⭐⭐⭐⭐"
                rating_color = QColor("#fbbf24")
            elif client.balance > 0:
                rating = "⭐⭐⭐"
                rating_color = QColor("#fbbf24")
            elif client.balance == 0:
                rating = "⭐⭐"
                rating_color = QColor("#9ca3af")
            else:
                rating = "⭐"
                rating_color = QColor("#ef4444")

            rating_item = QTableWidgetItem(rating)
            rating_item.setTextAlignment(Qt.AlignCenter)
            rating_item.setFont(QFont("Arial", 12))
            rating_item.setForeground(rating_color)
            self.clients_table.setItem(row, 9, rating_item)

    # دوال الإجراءات
    def add_client(self):
        show_info_message("قريباً", "سيتم إضافة هذه الميزة قريباً")

    def edit_client(self):
        show_info_message("قريباً", "سيتم إضافة هذه الميزة قريباً")

    def delete_client(self):
        show_info_message("قريباً", "سيتم إضافة هذه الميزة قريباً")

    def view_client(self):
        show_info_message("قريباً", "سيتم إضافة هذه الميزة قريباً")

    def generate_client_report(self):
        show_info_message("قريباً", "سيتم إضافة هذه الميزة قريباً")

    def export_clients(self):
        show_info_message("قريباً", "سيتم إضافة هذه الميزة قريباً")
