#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        print("Starting quick test...")
        
        # Test basic imports
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 imported successfully")
        
        # Create minimal app
        app = QApplication(sys.argv)
        print("✅ QApplication created")
        
        # Test database
        from database.database import SessionLocal
        session = SessionLocal()
        print("✅ Database session created")
        
        # Test client model
        from database.models import Client
        clients = session.query(Client).all()
        print(f"✅ Found {len(clients)} clients in database")
        
        # Test clients widget creation
        from ui.clients import ClientsWidget
        print("✅ ClientsWidget imported")
        
        widget = ClientsWidget(session)
        print("✅ ClientsWidget created successfully")
        
        print(f"📊 Table rows: {widget.clients_table.rowCount()}")
        print(f"📊 Table columns: {widget.clients_table.columnCount()}")
        
        # Test simple enhancements
        if widget.clients_table.rowCount() > 0:
            print("🎨 Testing simple enhancements...")
            widget.apply_simple_enhancements()
            print("✅ Simple enhancements applied")
        else:
            print("⚠️ No data to test enhancements")
            
        print("🎉 All tests passed!")
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
