#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لتحديث جدول الموردين لإضافة حقل الحالة
"""

import sqlite3
import os
from datetime import datetime

def update_suppliers_table():
    """تحديث جدول الموردين لإضافة حقل status"""
    
    db_path = "accounting.db"
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 فحص هيكل جدول الموردين...")
        
        # فحص الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(suppliers)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"📋 الأعمدة الموجودة: {column_names}")
        
        # التحقق من وجود عمود status
        if 'status' not in column_names:
            print("➕ إضافة عمود status...")
            cursor.execute("ALTER TABLE suppliers ADD COLUMN status TEXT DEFAULT 'نشط'")
            print("✅ تم إضافة عمود status بنجاح")
        else:
            print("✅ عمود status موجود مسبقاً")
        
        # التحقق من وجود عمود updated_at
        if 'updated_at' not in column_names:
            print("➕ إضافة عمود updated_at...")
            cursor.execute("ALTER TABLE suppliers ADD COLUMN updated_at DATETIME")
            
            # تحديث القيم الموجودة
            cursor.execute("""
                UPDATE suppliers 
                SET updated_at = created_at 
                WHERE updated_at IS NULL
            """)
            print("✅ تم إضافة عمود updated_at بنجاح")
        else:
            print("✅ عمود updated_at موجود مسبقاً")
        
        # تحديث الموردين الذين ليس لديهم حالة
        cursor.execute("UPDATE suppliers SET status = 'نشط' WHERE status IS NULL OR status = ''")
        updated_rows = cursor.rowcount
        
        if updated_rows > 0:
            print(f"🔄 تم تحديث {updated_rows} مورد بالحالة الافتراضية 'نشط'")
        
        # حفظ التغييرات
        conn.commit()
        
        # عرض إحصائيات
        cursor.execute("SELECT COUNT(*) FROM suppliers")
        total_suppliers = cursor.fetchone()[0]
        
        cursor.execute("SELECT status, COUNT(*) FROM suppliers GROUP BY status")
        status_counts = cursor.fetchall()
        
        print("\n📊 إحصائيات الموردين:")
        print(f"📋 إجمالي الموردين: {total_suppliers}")
        for status, count in status_counts:
            print(f"   {status}: {count}")
        
        conn.close()
        
        print("\n🎉 تم تحديث قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {str(e)}")
        return False

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات قبل التحديث"""
    
    db_path = "accounting.db"
    if not os.path.exists(db_path):
        return False
    
    try:
        backup_name = f"accounting_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        backup_path = os.path.join("backups", backup_name)
        
        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        os.makedirs("backups", exist_ok=True)
        
        # نسخ قاعدة البيانات
        import shutil
        shutil.copy2(db_path, backup_path)
        
        print(f"💾 تم إنشاء نسخة احتياطية: {backup_path}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء تحديث قاعدة البيانات...")
    print("=" * 50)
    
    # إنشاء نسخة احتياطية أولاً
    print("💾 إنشاء نسخة احتياطية...")
    if backup_database():
        print("✅ تم إنشاء النسخة الاحتياطية بنجاح")
    else:
        print("⚠️ لم يتم إنشاء نسخة احتياطية")
    
    print("\n🔧 تحديث جدول الموردين...")
    if update_suppliers_table():
        print("\n🎉 تم التحديث بنجاح! يمكنك الآن تشغيل البرنامج.")
    else:
        print("\n❌ فشل في التحديث!")
    
    print("=" * 50)
    input("اضغط Enter للخروج...")
