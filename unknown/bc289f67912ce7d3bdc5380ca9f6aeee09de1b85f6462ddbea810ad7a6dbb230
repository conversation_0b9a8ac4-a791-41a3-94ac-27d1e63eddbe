#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التحسينات المرئية لجدول العملاء
"""

import sys
import os
sys.path.append('.')

def test_client_enhancements():
    """اختبار التحسينات المرئية"""
    try:
        print("🚀 بدء اختبار التحسينات المرئية...")
        
        # استيراد المكتبات المطلوبة
        from ui.clients import ClientsWidget
        from database.database import SessionLocal
        from database.models import Client
        
        # إنشاء جلسة قاعدة البيانات
        session = SessionLocal()
        
        # إنشاء جدول العملاء
        print("📊 إنشاء جدول العملاء...")
        widget = ClientsWidget(session)
        
        print(f"✅ تم إنشاء الجدول بنجاح")
        print(f"📋 عدد الصفوف: {widget.clients_table.rowCount()}")
        print(f"📊 عدد الأعمدة: {widget.clients_table.columnCount()}")
        
        # التحقق من وجود البيانات
        if widget.clients_table.rowCount() > 0:
            print("🎨 اختبار التحسينات المرئية...")
            
            # اختبار التحسينات المرئية
            widget.add_visual_enhancements()
            print("✅ تم تطبيق التحسينات المرئية")
            
            # اختبار تمييز البيانات
            widget.highlight_important_data()
            print("✅ تم تطبيق تمييز البيانات")
            
            # اختبار مؤشرات الحالة
            widget.add_status_indicators()
            print("✅ تم تطبيق مؤشرات الحالة")
            
            print("🎉 تم تطبيق جميع التحسينات بنجاح!")
            
            # عرض معلومات الجدول
            print("\n📊 معلومات الجدول:")
            for row in range(min(3, widget.clients_table.rowCount())):  # أول 3 صفوف
                row_data = []
                for col in range(widget.clients_table.columnCount()):
                    item = widget.clients_table.item(row, col)
                    if item:
                        row_data.append(item.text()[:20])  # أول 20 حرف
                    else:
                        row_data.append("فارغ")
                print(f"   الصف {row + 1}: {' | '.join(row_data)}")
                
        else:
            print("⚠️ لا توجد بيانات في الجدول")
            print("🧪 إنشاء بيانات تجريبية...")
            widget.create_test_data_with_enhancements()
            print(f"📋 عدد الصفوف بعد إضافة البيانات: {widget.clients_table.rowCount()}")
            
        # اختبار فرض التحسينات
        print("\n🔧 اختبار فرض التحسينات...")
        widget.force_apply_enhancements()
        
        # التحقق من حالة التحسينات
        print(f"\n📈 حالة التحسينات:")
        print(f"   🎨 التحسينات مطبقة: {widget.enhancements_applied}")
        print(f"   📊 عدد الصفوف النهائي: {widget.clients_table.rowCount()}")
        print(f"   📋 عدد الأعمدة النهائي: {widget.clients_table.columnCount()}")
        
        # اختبار الإحصائيات
        if hasattr(widget, 'live_stats'):
            print(f"\n📈 الإحصائيات المباشرة:")
            stats = widget.live_stats
            print(f"   👥 إجمالي العملاء: {stats.get('total_clients', 0)}")
            print(f"   ✅ العملاء النشطين: {stats.get('active_clients', 0)}")
            print(f"   ⚠️ العملاء المدينين: {stats.get('debtor_clients', 0)}")
            print(f"   💰 إجمالي الرصيد: {stats.get('total_balance', 0):.2f}")
            
        # اختبار التنبيهات
        if hasattr(widget, 'notification_system'):
            print(f"\n🔔 نظام التنبيهات:")
            notifications = widget.notification_system
            print(f"   ⚠️ عملاء متأخرين: {len(notifications.get('overdue_clients', []))}")
            print(f"   💎 عملاء عالي القيمة: {len(notifications.get('high_value_clients', []))}")
            print(f"   😴 عملاء غير نشطين: {len(notifications.get('inactive_clients', []))}")
            print(f"   💰 تذكيرات الدفع: {len(notifications.get('payment_reminders', []))}")
            
        # اختبار الرؤى الذكية
        if hasattr(widget, 'analytics_engine'):
            print(f"\n🧠 محرك التحليل الذكي:")
            analytics = widget.analytics_engine
            insights = analytics.get('insights', [])
            print(f"   💡 عدد الرؤى: {len(insights)}")
            for i, insight in enumerate(insights[:3]):  # أول 3 رؤى
                print(f"   {i+1}. {insight.get('title', 'رؤية')}")
                
        print("\n🎉 انتهى اختبار التحسينات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحسينات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        try:
            session.close()
        except:
            pass

if __name__ == "__main__":
    success = test_client_enhancements()
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
        sys.exit(0)
    else:
        print("\n❌ فشل في بعض الاختبارات!")
        sys.exit(1)
