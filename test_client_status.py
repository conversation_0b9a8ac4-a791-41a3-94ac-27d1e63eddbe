#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تطوير عمود الحالة في جدول العملاء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# استيراد الواجهات
from ui.clients import ClientsWidget
from database import init_database, get_session, Client

class TestClientStatusWindow(QMainWindow):
    """نافذة اختبار تطوير عمود الحالة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار تطوير عمود الحالة - العملاء")
        self.setGeometry(100, 100, 1400, 800)
        
        # إعداد قاعدة البيانات
        init_database()
        self.session = get_session()
        
        # إنشاء بيانات تجريبية
        self.create_test_data()
        
        # إعداد الواجهة
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # إضافة واجهة العملاء
        self.clients_widget = ClientsWidget(self.session)
        layout.addWidget(self.clients_widget)
        
        # تحديث البيانات
        self.clients_widget.refresh_data()
        
    def create_test_data(self):
        """إنشاء بيانات تجريبية لاختبار الحالات المختلفة"""
        try:
            # التحقق من وجود البيانات
            existing_clients = self.session.query(Client).count()
            if existing_clients > 0:
                print(f"✅ يوجد {existing_clients} عميل في قاعدة البيانات")
                return
            
            # إنشاء عملاء تجريبيين لاختبار الحالات المختلفة
            test_clients = [
                # عملاء نشطين (رصيد موجب)
                Client(
                    name="أحمد محمد السعيد",
                    phone="0501234567",
                    email="<EMAIL>",
                    address="الرياض - حي النخيل",
                    balance=15000.0,
                    notes="عميل VIP - نشط جداً"
                ),
                Client(
                    name="فاطمة علي الزهراني",
                    phone="0509876543",
                    email="<EMAIL>",
                    address="جدة - حي الصفا",
                    balance=8500.0,
                    notes="عميل مميز"
                ),
                Client(
                    name="محمد سالم القحطاني",
                    phone="0551122334",
                    email="<EMAIL>",
                    address="الدمام - حي الفيصلية",
                    balance=2500.0,
                    notes="عميل جيد"
                ),
                
                # عملاء عاديين (رصيد صفر)
                Client(
                    name="نورا خالد العتيبي",
                    phone="0556677889",
                    email="<EMAIL>",
                    address="مكة المكرمة - العزيزية",
                    balance=0.0,
                    notes="عميل جديد - لم يتم التعامل معه بعد"
                ),
                Client(
                    name="عبدالله أحمد الغامدي",
                    phone="0544455667",
                    email="<EMAIL>",
                    address="المدينة المنورة - قباء",
                    balance=0.0,
                    notes="عميل عادي - رصيد متوازن"
                ),
                Client(
                    name="سارة محمد الحربي",
                    phone="0533344556",
                    email="<EMAIL>",
                    address="الطائف - الحوية",
                    balance=0.0,
                    notes="عميل جديد"
                ),
                
                # عملاء مدينين (رصيد سالب)
                Client(
                    name="خالد عبدالرحمن الشهري",
                    phone="0522233445",
                    email="<EMAIL>",
                    address="أبها - المنهل",
                    balance=-5000.0,
                    notes="عميل مدين - يحتاج متابعة"
                ),
                Client(
                    name="منى سعد الدوسري",
                    phone="0511122334",
                    email="<EMAIL>",
                    address="الخبر - الراكة",
                    balance=-12000.0,
                    notes="عميل عالي المخاطر"
                ),
                Client(
                    name="يوسف إبراهيم المطيري",
                    phone="0500998877",
                    email="<EMAIL>",
                    address="بريدة - الصالحية",
                    balance=-800.0,
                    notes="مدين بمبلغ بسيط"
                )
            ]
            
            # إضافة العملاء لقاعدة البيانات
            for client in test_clients:
                self.session.add(client)
            
            self.session.commit()
            print(f"✅ تم إنشاء {len(test_clients)} عميل تجريبي بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات التجريبية: {str(e)}")
            self.session.rollback()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد الخط العربي
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    # إعداد اتجاه النص
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة الرئيسية
    window = TestClientStatusWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
