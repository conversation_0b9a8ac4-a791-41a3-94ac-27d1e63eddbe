#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('.')

try:
    print("Testing imports...")
    from PyQt5.QtWidgets import QApplication, QTableWidget, QTableWidgetItem
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QColor
    
    print("Creating QApplication...")
    app = QApplication(sys.argv)
    
    print("Creating table...")
    table = QTableWidget(2, 3)
    table.setHorizontalHeaderLabels(["ID", "Name", "Balance"])
    
    # Add test data
    table.setItem(0, 0, QTableWidgetItem("1"))
    table.setItem(0, 1, QTableWidgetItem("Test Client"))
    table.setItem(0, 2, QTableWidgetItem("-5000.00"))
    
    table.setItem(1, 0, QTableWidgetItem("2"))
    table.setItem(1, 1, QTableWidgetItem("VIP Client"))
    table.setItem(1, 2, QTableWidgetItem("15000.00"))
    
    print("Applying visual enhancements...")
    
    # Apply colors based on balance
    for row in range(table.rowCount()):
        balance_item = table.item(row, 2)
        if balance_item:
            balance = float(balance_item.text().replace(',', ''))
            
            if balance > 10000:
                # High value client - green
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    if item:
                        item.setBackground(QColor(200, 255, 200))
                        
            elif balance < -1000:
                # High risk client - red
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    if item:
                        item.setBackground(QColor(255, 200, 200))
    
    # Add status indicators
    for row in range(table.rowCount()):
        id_item = table.item(row, 0)
        balance_item = table.item(row, 2)
        if id_item and balance_item:
            client_id = id_item.text()
            balance = float(balance_item.text().replace(',', ''))
            
            if balance > 10000:
                id_item.setText(f"💎 {client_id}")  # VIP
            elif balance < -1000:
                id_item.setText(f"🚨 {client_id}")  # Risk
            else:
                id_item.setText(f"✅ {client_id}")  # Normal
    
    print("Visual enhancements applied successfully!")
    print(f"Table rows: {table.rowCount()}")
    print(f"Table columns: {table.columnCount()}")
    
    # Show table content
    for row in range(table.rowCount()):
        row_data = []
        for col in range(table.columnCount()):
            item = table.item(row, col)
            if item:
                row_data.append(item.text())
            else:
                row_data.append("Empty")
        print(f"Row {row + 1}: {' | '.join(row_data)}")
    
    print("Test completed successfully!")
    
except Exception as e:
    print(f"Error: {str(e)}")
    import traceback
    traceback.print_exc()
