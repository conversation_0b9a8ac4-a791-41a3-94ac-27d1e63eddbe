"""
نمط موحد للقوائم المنسدلة في جميع أنحاء التطبيق
يوفر تصميم متسق وجميل لجميع القوائم المنسدلة
"""

from PyQt5.QtWidgets import QMenu, QAction
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont


class UnifiedDropdownStyles:
    """فئة لإدارة الأنماط الموحدة للقوائم المنسدلة"""

    # ألوان موحدة للقوائم المنسدلة
    COLORS = {
        'primary': {
            'bg_main': '#ffffff',
            'bg_secondary': '#f8fafc',
            'bg_tertiary': '#e2e8f0',
            'border_main': '#3b82f6',
            'border_secondary': '#60a5fa',
            'text_primary': '#1e293b',
            'text_secondary': '#475569',
            'text_inverse': '#ffffff',
            'hover_bg': '#3b82f6',
            'hover_border': '#2563eb',
            'pressed_bg': '#1d4ed8',
            'pressed_border': '#1e40af',
            'disabled_bg': '#f1f5f9',
            'disabled_text': '#94a3b8',
            'separator': '#cbd5e1'
        },
        'success': {
            'bg_main': '#f0fdf4',
            'bg_secondary': '#dcfce7',
            'bg_tertiary': '#bbf7d0',
            'border_main': '#22c55e',
            'border_secondary': '#4ade80',
            'text_primary': '#14532d',
            'text_secondary': '#166534',
            'text_inverse': '#ffffff',
            'hover_bg': '#22c55e',
            'hover_border': '#16a34a',
            'pressed_bg': '#15803d',
            'pressed_border': '#166534',
            'disabled_bg': '#f1f5f9',
            'disabled_text': '#94a3b8',
            'separator': '#86efac'
        },
        'warning': {
            'bg_main': '#fffbeb',
            'bg_secondary': '#fef3c7',
            'bg_tertiary': '#fde68a',
            'border_main': '#f59e0b',
            'border_secondary': '#fbbf24',
            'text_primary': '#92400e',
            'text_secondary': '#b45309',
            'text_inverse': '#ffffff',
            'hover_bg': '#f59e0b',
            'hover_border': '#d97706',
            'pressed_bg': '#b45309',
            'pressed_border': '#92400e',
            'disabled_bg': '#f1f5f9',
            'disabled_text': '#94a3b8',
            'separator': '#fcd34d'
        },
        'danger': {
            'bg_main': '#fef2f2',
            'bg_secondary': '#fecaca',
            'bg_tertiary': '#fca5a5',
            'border_main': '#ef4444',
            'border_secondary': '#f87171',
            'text_primary': '#7f1d1d',
            'text_secondary': '#991b1b',
            'text_inverse': '#ffffff',
            'hover_bg': '#ef4444',
            'hover_border': '#dc2626',
            'pressed_bg': '#b91c1c',
            'pressed_border': '#991b1b',
            'disabled_bg': '#f1f5f9',
            'disabled_text': '#94a3b8',
            'separator': '#f87171'
        },
        'info': {
            'bg_main': '#f0f9ff',
            'bg_secondary': '#e0f2fe',
            'bg_tertiary': '#bae6fd',
            'border_main': '#0ea5e9',
            'border_secondary': '#38bdf8',
            'text_primary': '#0c4a6e',
            'text_secondary': '#075985',
            'text_inverse': '#ffffff',
            'hover_bg': '#0ea5e9',
            'hover_border': '#0284c7',
            'pressed_bg': '#0369a1',
            'pressed_border': '#075985',
            'disabled_bg': '#f1f5f9',
            'disabled_text': '#94a3b8',
            'separator': '#7dd3fc'
        },
        'dark': {
            'bg_main': '#1e293b',
            'bg_secondary': '#334155',
            'bg_tertiary': '#475569',
            'border_main': '#64748b',
            'border_secondary': '#94a3b8',
            'text_primary': '#f1f5f9',
            'text_secondary': '#e2e8f0',
            'text_inverse': '#0f172a',
            'hover_bg': '#475569',
            'hover_border': '#64748b',
            'pressed_bg': '#334155',
            'pressed_border': '#475569',
            'disabled_bg': '#0f172a',
            'disabled_text': '#64748b',
            'separator': '#64748b'
        }
    }

    @staticmethod
    def get_dropdown_style(color_scheme='primary', size='normal'):
        """
        الحصول على نمط القائمة المنسدلة

        Args:
            color_scheme (str): نظام الألوان ('primary', 'success', 'warning', 'danger', 'info', 'dark')
            size (str): حجم القائمة ('small', 'normal', 'large')

        Returns:
            str: نمط CSS للقائمة المنسدلة
        """
        colors = UnifiedDropdownStyles.COLORS.get(color_scheme, UnifiedDropdownStyles.COLORS['primary'])

        # تحديد الأحجام
        sizes = {
            'small': {
                'font_size': '14px',
                'padding': '8px 12px',
                'margin': '2px',
                'border_radius': '8px',
                'border_width': '2px',
                'min_width': '180px',
                'max_width': '250px',
                'item_height': '28px'
            },
            'normal': {
                'font_size': '16px',
                'padding': '12px 16px',
                'margin': '3px',
                'border_radius': '12px',
                'border_width': '3px',
                'min_width': '220px',
                'max_width': '300px',
                'item_height': '36px'
            },
            'large': {
                'font_size': '18px',
                'padding': '16px 20px',
                'margin': '4px',
                'border_radius': '16px',
                'border_width': '4px',
                'min_width': '260px',
                'max_width': '350px',
                'item_height': '44px'
            }
        }

        size_config = sizes.get(size, sizes['normal'])

        return f"""
            QMenu {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['bg_main']},
                    stop:0.1 {colors['bg_secondary']},
                    stop:0.9 {colors['bg_tertiary']},
                    stop:1 {colors['bg_secondary']});
                border: {size_config['border_width']} solid {colors['border_main']};
                border-radius: {size_config['border_radius']};
                padding: 8px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: {size_config['font_size']};
                font-weight: 600;
                min-width: {size_config['min_width']};
                max-width: {size_config['max_width']};
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15),
                           0 4px 10px rgba(0, 0, 0, 0.1);
            }}

            QMenu::item {{
                background: transparent;
                padding: {size_config['padding']};
                border-radius: 8px;
                margin: {size_config['margin']};
                color: {colors['text_primary']};
                font-size: {size_config['font_size']};
                font-weight: 600;
                border: 2px solid transparent;
                min-height: {size_config['item_height']};
                text-align: right;
            }}

            QMenu::item:selected {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['hover_bg']},
                    stop:1 {colors['hover_border']});
                color: {colors['text_inverse']};
                border: 2px solid {colors['hover_border']};
                font-weight: 700;
                border-radius: 10px;
            }}

            QMenu::item:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['pressed_bg']},
                    stop:1 {colors['pressed_border']});
                color: {colors['text_inverse']};
                border: 2px solid {colors['pressed_border']};
                font-weight: 700;
                border-radius: 8px;
            }}

            QMenu::item:disabled {{
                background: {colors['disabled_bg']};
                color: {colors['disabled_text']};
                border: 2px solid transparent;
                font-weight: 400;
            }}

            QMenu::separator {{
                height: 2px;
                background: {colors['separator']};
                margin: 8px 16px;
                border-radius: 1px;
            }}

            QMenu::icon {{
                padding-left: 8px;
                padding-right: 8px;
                width: 16px;
                height: 16px;
            }}
        """

    @staticmethod
    def apply_style_to_menu(menu, color_scheme='primary', size='normal'):
        """
        تطبيق النمط على قائمة منسدلة

        Args:
            menu (QMenu): القائمة المنسدلة
            color_scheme (str): نظام الألوان
            size (str): حجم القائمة
        """
        try:
            style = UnifiedDropdownStyles.get_dropdown_style(color_scheme, size)
            menu.setStyleSheet(style)

            # تطبيق خط موحد
            font = QFont("Segoe UI", 16 if size == 'normal' else (14 if size == 'small' else 18), QFont.Bold)
            font.setLetterSpacing(QFont.AbsoluteSpacing, 0.5)
            menu.setFont(font)

        except Exception as e:
            print(f"❌ خطأ في تطبيق نمط القائمة المنسدلة: {str(e)}")

    @staticmethod
    def create_styled_menu(parent, title="", color_scheme='primary', size='normal'):
        """
        إنشاء قائمة منسدلة بنمط موحد

        Args:
            parent: العنصر الأب
            title (str): عنوان القائمة
            color_scheme (str): نظام الألوان
            size (str): حجم القائمة

        Returns:
            QMenu: القائمة المنسدلة المنسقة
        """
        try:
            menu = QMenu(title, parent)
            UnifiedDropdownStyles.apply_style_to_menu(menu, color_scheme, size)
            return menu
        except Exception as e:
            print(f"❌ خطأ في إنشاء القائمة المنسدلة: {str(e)}")
            return QMenu(title, parent)

    @staticmethod
    def add_styled_action(menu, text, icon=None, callback=None, enabled=True):
        """
        إضافة عنصر منسق للقائمة المنسدلة

        Args:
            menu (QMenu): القائمة المنسدلة
            text (str): نص العنصر
            icon: أيقونة العنصر (اختياري)
            callback: دالة الاستدعاء (اختياري)
            enabled (bool): هل العنصر مفعل

        Returns:
            QAction: العنصر المضاف
        """
        try:
            action = QAction(text, menu)

            if icon:
                action.setIcon(icon)

            if callback:
                action.triggered.connect(callback)

            action.setEnabled(enabled)
            menu.addAction(action)

            return action
        except Exception as e:
            print(f"❌ خطأ في إضافة عنصر للقائمة: {str(e)}")
            return None

    @staticmethod
    def get_combobox_style(color_scheme='primary', size='normal'):
        """
        الحصول على نمط QComboBox محسن

        Args:
            color_scheme (str): نظام الألوان
            size (str): حجم العنصر

        Returns:
            str: نمط CSS للـ QComboBox
        """
        colors = UnifiedDropdownStyles.COLORS.get(color_scheme, UnifiedDropdownStyles.COLORS['primary'])

        # تحديد الأحجام
        sizes = {
            'small': {
                'font_size': '14px',
                'padding': '8px 12px',
                'border_radius': '8px',
                'min_height': '20px',
                'arrow_size': '6px'
            },
            'normal': {
                'font_size': '16px',
                'padding': '12px 16px',
                'border_radius': '12px',
                'min_height': '24px',
                'arrow_size': '8px'
            },
            'large': {
                'font_size': '18px',
                'padding': '16px 20px',
                'border_radius': '16px',
                'min_height': '28px',
                'arrow_size': '10px'
            }
        }

        size_config = sizes.get(size, sizes['normal'])

        return f"""
            QComboBox {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['bg_main']},
                    stop:0.1 {colors['bg_secondary']},
                    stop:0.9 {colors['bg_tertiary']},
                    stop:1 {colors['bg_main']});
                border: 3px solid {colors['border_main']};
                border-radius: {size_config['border_radius']};
                padding: {size_config['padding']};
                font-size: {size_config['font_size']};
                font-weight: 700;
                min-height: {size_config['min_height']};
                text-align: center;
                color: {colors['text_primary']};
                letter-spacing: 0.5px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
                           inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }}

            QComboBox:hover {{
                border: 3px solid {colors['border_secondary']};
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['hover_bg']},
                    stop:0.5 {colors['bg_main']},
                    stop:1 {colors['hover_bg']});
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25),
                           inset 0 1px 0 rgba(255, 255, 255, 0.4);
            }}

            QComboBox:focus {{
                border: 3px solid {colors['pressed_border']};
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['pressed_bg']},
                    stop:0.5 {colors['bg_main']},
                    stop:1 {colors['pressed_bg']});
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.5);
            }}

            QComboBox::drop-down {{
                border: none;
                width: 30px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {colors['border_main']},
                    stop:1 {colors['border_secondary']});
                border-radius: 8px;
                margin: 2px;
            }}

            QComboBox::down-arrow {{
                image: none;
                border-left: {size_config['arrow_size']} solid transparent;
                border-right: {size_config['arrow_size']} solid transparent;
                border-top: {size_config['arrow_size']} solid {colors['border_main']};
                margin-right: 8px;
            }}

            QComboBox::down-arrow:hover {{
                border-top: {size_config['arrow_size']} solid {colors['border_secondary']};
            }}

            QComboBox QAbstractItemView {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['bg_main']},
                    stop:0.1 {colors['bg_secondary']},
                    stop:0.9 {colors['bg_tertiary']},
                    stop:1 {colors['bg_main']});
                border: 3px solid {colors['border_main']};
                border-radius: {size_config['border_radius']};
                padding: 8px;
                font-size: {size_config['font_size']};
                font-weight: 700;
                text-align: center;
                selection-background-color: {colors['hover_bg']};
                selection-color: {colors['text_inverse']};
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }}

            QComboBox QAbstractItemView::item {{
                background: transparent;
                padding: {size_config['padding']};
                border-radius: 8px;
                margin: 2px;
                color: {colors['text_primary']};
                font-weight: 700;
                text-align: center;
                min-height: {size_config['min_height']};
                border: 2px solid transparent;
            }}

            QComboBox QAbstractItemView::item:selected {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {colors['hover_bg']},
                    stop:1 {colors['border_main']});
                color: {colors['text_inverse']};
                border: 2px solid rgba(255, 255, 255, 0.8);
                font-weight: 900;
                border-radius: 10px;
            }}

            QComboBox QAbstractItemView::item:hover {{
                background: {colors['bg_tertiary']};
                border: 2px solid {colors['border_main']};
                color: {colors['text_primary']};
                font-weight: 700;
            }}
        """
